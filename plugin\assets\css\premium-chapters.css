/* Premium chapter styling for manga details page - Higher specificity */
.list-body-hh .item a.cairo-premium,
a.cairo-premium {
    color: #ffc71e !important;
    background: #ffc71e24 !important;
    border: thin solid #ffc71e91 !important;
    border-radius: 6px !important;
    position: relative !important;
}

.list-body-hh .item a.cairo-premium:hover,
a.cairo-premium:hover {
    background: #ffc71e40 !important;
    border-color: #ffc71e !important;
    color: #ffc71e !important;
}

.list-body-hh .item a.cairo-premium::after,
a.cairo-premium::after {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    opacity: 0.8;
    color: #ffc71e;
}

.c-premium-tag {
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%) !important;
    color: white !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
}

.premium-ch {
    background: linear-gradient(135deg, #ffc71e 0%, #ffb347 100%) !important;
    color: #333 !important;
    padding: 0px 8px !important;
    border-radius: 12px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 8px rgba(255, 199, 30, 0.3) !important;
}

.premium-chapter {
    position: relative;
}

.premium-chapter::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 199, 30, 0.1) 50%, transparent 70%);
    pointer-events: none;
    border-radius: 4px;
}

/* Hide premium content for non-subscribers */
body.premium-chapter-blocked .page-break {
    display: none !important;
}

body.premium-chapter-blocked .wp-manga-chapter-img {
    display: none !important;
}

body.premium-chapter-blocked .reading-content .page-break {
    display: none !important;
}

/* Enhanced Subscription Message Components */
.subscription-required-message {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
    border: 2px solid #2a2a2a;
    border-radius: 20px;
    padding: 48px;
    text-align: center;
    color: white;
    margin: 40px 0;
    position: relative;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Inter', sans-serif;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.subscription-required-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 199, 30, 0.05) 50%, transparent 70%);
    pointer-events: none;
}

.message-content {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

/* Icon Styling */
.message-icon-container {
    position: relative;
    display: inline-block;
    margin-bottom: 24px;
}

.message-icon {
    font-size: 4.5rem;
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 4px 12px rgba(255, 199, 30, 0.3));
}

.icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(255, 199, 30, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse-glow 2s ease-in-out infinite;
}

.premium-glow {
    background: radial-gradient(circle, rgba(255, 107, 53, 0.3) 0%, transparent 70%);
}

@keyframes pulse-glow {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.6; }
    50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
}

/* Premium Badge */
/*
.premium-badge {
    display: inline-block;
    background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 16px;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}
*/
/* Typography */
.subscription-required-message h3 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 20px 0;
    background: linear-gradient(135deg, #FF6B35 0%, #ffc71e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.subscription-required-message p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 32px 0;
    line-height: 1.6;
    font-weight: 400;
    text-align: center;
}

/* Login Benefits */
.login-benefits {
    background: rgba(255, 199, 30, 0.08);
    border: 1px solid rgba(255, 199, 30, 0.2);
    border-radius: 16px;
    padding: 24px;
    margin: 32px 0;
    text-align: left;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 12px 0;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.benefit-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

/* Premium Stats */
.premium-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    margin: 32px 0;
    padding: 24px;
    background: rgba(255, 107, 53, 0.08);
    border: 1px solid rgba(255, 107, 53, 0.2);
    border-radius: 16px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #ffc71e;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-divider {
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
}

/* Premium Benefits Grid */
.subscription-benefits-premium {
    background: rgba(255, 107, 53, 0.08);
    border: 1px solid rgba(255, 107, 53, 0.2);
    border-radius: 16px;
    padding: 28px;
    margin: 32px 0;
    text-align: left;
}

.subscription-benefits-premium h4 {
    color: #FF6B35;
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    font-weight: 700;
    text-align: center;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.benefits-grid .benefit-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 16px;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.benefits-grid .benefit-item:hover {
    background: rgba(255, 107, 53, 0.1);
    border-color: rgba(255, 107, 53, 0.3);
    transform: translateY(-2px);
}

/* Action Buttons */
.message-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 32px;
    gap: 16px;
}


/* Subscription Message Buttons - High Specificity */
.subscription-required-message .btn-primary-reading,
.subscription-required-message .btn-secondary-reading,
.subscription-required-message .btn-premium-cta {
    padding: 16px 32px !important;
    border-radius: 50px !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    border: none !important;
    font-size: 1.1rem !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 12px !important;
    position: relative !important;
    overflow: hidden !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Inter', sans-serif !important;
}

.subscription-required-message .btn-primary-reading {
    background: linear-gradient(135deg, #ff7533 0%, #f6b61f 100%) !important;
    color: white !important;
    box-shadow: 0 8px 32px rgb(227 131 50 / 40%) !important;
    min-width: 200px !important;
    justify-content: center !important;
}

.subscription-required-message .btn-primary-reading:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 35px rgba(255, 107, 53, 0.5) !important;
    color: white !important;
    text-decoration: none !important;
}


.subscription-required-message .btn-premium-cta {
    background: linear-gradient(135deg, #ff7533 0%, #f6b61f 100%) !important;
    color: white !important;
    box-shadow: 0 8px 32px rgb(227 131 50 / 40%) !important;
    padding: 20px 40px !important;
    font-size: 1.2rem !important;
    flex-direction: column !important;
    gap: 4px !important;
    min-width: 280px !important;
}

.subscription-required-message .btn-premium-cta:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 40px rgb(227 143 50 / 60%) !important;
    color: white !important;
    text-decoration: none !important;
}

.cta-text {
    font-weight: 700;
    font-size: 1.2rem;
}

.cta-price {
    font-size: 0.9rem;
    opacity: 0.9;
    font-weight: 500;
}

.subscription-required-message .btn-secondary-reading {
    background: rgba(255, 255, 255, 0.08) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-size: 1rem !important;
}

.subscription-required-message .btn-secondary-reading:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    color: white !important;
    text-decoration: none !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.subscription-required-message .btn-arrow {
    font-size: 1.2rem !important;
    transition: transform 0.3s ease !important;
}

.subscription-required-message .btn-primary-reading:hover .btn-arrow,
.subscription-required-message .btn-premium-cta:hover .btn-arrow {
    transform: translateX(4px) !important;
}

/* Footer Messages */
.login-footer, .premium-guarantee {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.login-footer p, .premium-guarantee p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

/* Section Title Spacing Fix */
.section-title {
    line-height: 1.3 !important;
    margin-bottom: 20px !important;
}

.section-title .gradient-text {
    display: block;
    margin-top: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subscription-required-message {
        padding: 32px 24px;
        margin: 20px 0;
        border-radius: 16px;
    }

    .message-icon {
        font-size: 3.5rem;
    }

    .subscription-required-message h3 {
        font-size: 2rem;
    }

    .subscription-required-message p {
        font-size: 1.1rem;
    }

    .premium-stats {
        flex-direction: column;
        gap: 20px;
    }

    .stat-divider {
        display: none;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .message-actions {
        gap: 12px;
    }

    .subscription-required-message .btn-primary-reading,
    .subscription-required-message .btn-secondary-reading,
    .subscription-required-message .btn-premium-cta {
        width: 100% !important;
        text-align: center !important;
        justify-content: center !important;
    }

    .subscription-required-message .btn-premium-cta {
        min-width: auto !important;
        padding: 18px 24px !important;
    }
}

@media (max-width: 480px) {
    .subscription-required-message {
        padding: 24px 16px;
    }

    .message-icon {
        font-size: 3rem;
    }

    .subscription-required-message h3 {
        font-size: 1.8rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .premium-stats {
        padding: 20px;
    }

    .subscription-benefits-premium,
    .login-benefits {
        padding: 20px;
    }
}