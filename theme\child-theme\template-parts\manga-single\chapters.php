<div class="list-menu">
    <form class="form-inline">
        <input class="form-control ch-filter cairo" type="text" placeholder="Numéro..." />
        <button class="btn">
            <i class="fa-regular fa-magnifying-glass"></i>
        </button>
    </form>
    <button class="btn btn-secondary1 ch-order">
        <span class="sort-text">Dernier</span>
        <i class="fa-solid fa-arrow-down-arrow-up"></i>
    </button>
</div>

<div class="list-body-hh">
    <ul class="scroll-sm">
        <?php foreach ($chapters as $chapter): 
            // Check if chapter is premium
            $is_premium = isset($chapter->chapter_id) ? get_post_meta($chapter->chapter_id, '_is_premium_chapter', true) : false;
            $cairo_class = ($is_premium === '1') ? 'cairo-premium' : 'cairo';
            $item_class = ($is_premium === '1') ? 'item premium-chapter' : 'item';

            $chapter_link = esc_url(trailingslashit(get_permalink($post_id)) . untrailingslashit($chapter->chapter_slug));
            if ($is_premium === '1') {
                if (!is_user_logged_in()) {
                    $chapter_link = esc_url(home_url('/connexion'));
                } elseif (function_exists('is_user_subscribed') && !is_user_subscribed()) {
                    $chapter_link = esc_url(home_url('/subscription/'));
                }
            }
        ?>
            <li class="<?php echo esc_attr($item_class); ?>" data-number="<?php echo esc_attr($chapter->chapter_id); ?>">
                <a class="<?php echo esc_attr($cairo_class); ?>" href="<?php echo $chapter_link; ?>" 
                   title="<?php echo esc_attr__('Chapitre', 'child-mfire') . ' ' . esc_attr($chapter->chapter_name); ?>">
                    <span>
                        <i class="fa-solid fa-play"></i>
                        <span>Chapitre</span> 
                        <?php echo esc_html($chapter->chapter_name); ?><?php echo esc_html($chapter->chapter_name_extend); ?>
                        <?php if ($is_premium === '1'): ?>
                            <span class="premium-ch">Premium</span>
                        <?php endif; ?>
                    </span>
                    <span>
                        <?php echo esc_html(time_ago($chapter->date)); ?>
                    </span>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const orderButton = document.querySelector('.ch-order');
    const filterInput = document.querySelector('.ch-filter');
    const list = document.querySelector('.scroll-sm');

    function extractNumber(text) {
        const match = text.match(/\d+(\.\d+)?/);
        return match ? parseFloat(match[0]) : 0;
    }

    function sortItems() {
        const items = Array.from(list.querySelectorAll('.item'));
        const isAscending = orderButton.classList.toggle('asc');
        orderButton.querySelector('.sort-text').textContent = isAscending ? 'Ancien' : 'Dernier';
        
        items.sort((a, b) => {
            const aNumber = extractNumber(a.querySelector('a').textContent);
            const bNumber = extractNumber(b.querySelector('a').textContent);
            return isAscending ? aNumber - bNumber : bNumber - aNumber;
        }).forEach(item => list.appendChild(item));
    }

    function filterItems() {
        const filterText = filterInput.value.toLowerCase();
        Array.from(list.querySelectorAll('.item')).forEach(item => {
            const numberText = extractNumber(item.querySelector('a').textContent).toString();
            item.style.display = numberText.includes(filterText) ? '' : 'none';
        });
    }

    orderButton.addEventListener('click', sortItems);
    filterInput.addEventListener('input', filterItems);
});
</script>