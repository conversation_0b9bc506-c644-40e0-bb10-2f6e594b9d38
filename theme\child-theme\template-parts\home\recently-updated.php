<section class="recently-updated">
    <div class="head">
        <h2><PERSON><PERSON><PERSON> Chapitres</h2>
    </div>

    <div id="recently-up-ajax" class="original card-lg">
        <?php
        // Get initial 14 manga posts
        $manga_data = [];
        
        // Query all published manga posts
        $args = array(
            'post_type' => 'wp-manga',
            'post_status' => 'publish',
            'posts_per_page' => -1,
        );
        
        $manga_query = new WP_Query($args);
        
        // Get manga with their latest chapter dates
        if ($manga_query->have_posts()) {
            while ($manga_query->have_posts()) {
                $manga_query->the_post();
                $manga_id = get_the_ID();
                $chapters = get_latest_chapters($manga_id, 1);
                
                // Handle adult content
                $adult_content_meta = get_post_meta($manga_id, 'manga_adult_content', true);
                $is_adult = is_array(maybe_unserialize($adult_content_meta)) && in_array('yes', maybe_unserialize($adult_content_meta));
                $show_adult = isset($_COOKIE['show_adult']) && $_COOKIE['show_adult'] === '1';
                
                if (!$show_adult && $is_adult) {
                    continue;
                }

                if ($chapters) {
                    $latest_chapter_date = strtotime($chapters[0]->date);
                    $manga_data[$manga_id] = $latest_chapter_date;
                }
            }
            wp_reset_postdata();

            // Sort by latest chapter date
            arsort($manga_data);
            
            // Get first 18 manga IDs
            $manga_ids = array_slice(array_keys($manga_data), 0, 21);

            // Query the 18 selected manga
            if (!empty($manga_ids)) {
                $args = array(
                    'post_type' => 'wp-manga',
                    'post_status' => 'publish',
                    'post__in' => $manga_ids,
                    'posts_per_page' => 21,
                    'orderby' => 'post__in',
                );
                
                $manga_query = new WP_Query($args);

                if ($manga_query->have_posts()) {
                    while ($manga_query->have_posts()) : $manga_query->the_post();
                        $manga_id = get_the_ID();
                        $manga_title = get_the_title();
                        $manga_link = get_permalink();
                        $chapters = get_latest_chapters($manga_id, 2);
                        ?>
                        
                        <div class="unit item-<?php echo esc_attr($manga_id); ?>">
                            <div class="inner">
                                <a href="<?php echo esc_url($manga_link); ?>" class="poster" data-tip="<?php echo esc_attr($manga_id); ?>">
                                    <div class="poster-image-wrapper shine-effect">
                                        <?php
                                        // Get comic type and flag
                                        $types = wp_get_post_terms($manga_id, 'wp-manga-type');
                                        $comic_type = !empty($types) ? $types[0]->name : '';
                                        $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                                        
                                        $type_to_flag = [
                                            'manga'  => 'jp.svg',
                                            'manhwa' => 'kr.svg',
                                            'manhua' => 'cn.svg'
                                        ];
                                        $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
                                            ? $flag_path . $type_to_flag[strtolower($comic_type)]
                                            : '';
                                        
                                        if($flag_image): ?>
                                        <div class="manga-flag width-limit" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                                            <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                                        </div>
                                        <?php endif; ?>
                                        <img src="<?php echo get_the_post_thumbnail_url( get_the_ID(), 'manga_cover' ); ?>" 
                                             alt="<?php the_title(); ?>" 
                                             style="height: 100%;object-fit: cover;"/>
                                    </div>
                                </a>
                                <div class="info"> 
                                    <a href="<?php echo esc_url($manga_link); ?>"><?php echo esc_html($manga_title); ?></a>
                                    
                                    <ul class="content" data-name="chap">
                                        <?php
                                        if ($chapters) {
                                            foreach ($chapters as $chapter) {
                                                // (changed from 6 to 12 hours)
                                                $time_diff = (current_time('timestamp') - strtotime($chapter->date)) / 108000;

                                                // Get chapter ID using manga ID and chapter slug
                                                global $wpdb;
                                                $chapter_id = $wpdb->get_var($wpdb->prepare(
                                                    "SELECT chapter_id FROM {$wpdb->prefix}manga_chapters WHERE post_id = %d AND chapter_slug = %s",
                                                    $manga_id,
                                                    $chapter->chapter_slug
                                                ));
                                                
                                                $is_premium = false;
                                                if ($chapter_id) {
                                                    $is_premium = get_post_meta($chapter_id, '_is_premium_chapter', true);
                                                }

                                                $chapter_link = esc_url(get_permalink($manga_id) . $chapter->chapter_slug);
                                                if ($is_premium === '1') {
                                                    if (!is_user_logged_in()) {
                                                        $chapter_link = esc_url(home_url('/connexion'));
                                                    } elseif (function_exists('is_user_subscribed') && !is_user_subscribed()) {
                                                        $chapter_link = esc_url(home_url('/subscription/'));
                                                    }
                                                }
                                                ?>
                                                <li>
                                                    <a href="<?php echo $chapter_link; ?>" 
                                                       class="<?php echo ($time_diff < 1) ? 'fire-chapter' : ''; ?>">
                                                        <span class="ch-num">Ch. <?php echo esc_html($chapter->chapter_name); ?></span>
                                                        <span class="ch-date">
                                                            <?php if ($time_diff < 1): ?>
                                                                <?php 
                                                                // Apply filter to allow plugins to modify the chapter tag
                                                                $chapter_tag = apply_filters('wp_manga_chapter_tag', 
                                                                    '<span class="new-chapter"><i class="fa-solid fa-fire"></i> New</span>', 
                                                                    $chapter, 
                                                                    $manga_id
                                                                );
                                                                echo $chapter_tag;
                                                                ?>
                                                            <?php else: ?>
                                                                <?php 
                                                                if ($is_premium === '1'): ?>
                                                                    <span class="premium-ch">Premium</span>
                                                                <?php else: ?>
                                                                    <?php echo esc_html(time_ago($chapter->date)); ?>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                        </span>
                                                    </a>
                                                </li>
                                            <?php }
                                        } ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php
                    endwhile;
                    wp_reset_postdata();
                }
            }
        }
        ?>
    </div>
    
    <!-- Backup -->
    <!-- <div class="load-more-wrapper text-center">
        <a href="<?php echo esc_url(home_url('/?post_type=wp-manga&s=')); ?>" class="btn btn-primary" id="view-all">
            <span class="button-text">View All</span>
        </a>
    </div> -->

    <div class="load-more-wrapper text-center">
        <!-- Changed id to load-more-manga and added spinner element -->
        <a href="#" class="btn btn-primary" id="load-more-manga">
            <span class="button-text">Afficher plus</span>
            <span class="sloading d-none"></span> 
        </a>
    </div>
    <?php
    $sponsored_options = get_option('sponsored_content_settings');
    if (isset($sponsored_options['enable_recently_updated']) && $sponsored_options['enable_recently_updated']) {
        $link = !empty($sponsored_options['link_recently_updated']) ? esc_url($sponsored_options['link_recently_updated']) : '#';
        $image = !empty($sponsored_options['image_recently_updated']) ? esc_url($sponsored_options['image_recently_updated']) : ''; // Default to empty if not set
        if (!empty($image)) { // Only display if image is set
        ?>
        <br>
        <a href="<?php echo $link; ?>" target="_blank">
            <img src="<?php echo $image; ?>" alt="Sponsored Content" style="max-width: 100% !important; width: 100%;" />
        </a>
    <?php }
    } ?>
</section>


