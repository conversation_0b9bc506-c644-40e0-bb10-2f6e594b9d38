<?php 

// Define the status mapping
$status_mapping = array(
    'on-going'   => 'En cours',
    'end'        => 'Termin<PERSON>',
    'canceled'   => 'Annulé',
    'on-hold'    => 'En pause',
    'upcoming'   => 'A venir',
);

// Fetch all unique manga statuses (using WP_Query)
$statuses = get_posts(array(
    'post_type'      => 'wp-manga',
    'meta_key'       => '_wp_manga_status',
    'fields'         => 'ids',
    'posts_per_page' => -1,
));

// Get unique statuses efficiently
$unique_statuses = array_unique(array_filter(array_map(function($manga_id) {
    return get_post_meta($manga_id, '_wp_manga_status', true);
}, $statuses)));

// Define the number of posts per page
$posts_per_page = 21; // Changed from get_option('posts_per_page') to fixed value of 21

// Get current page number
$paged = max(1, get_query_var('paged', 1));

// Check if there's a search keyword
$search_keyword = get_query_var('s', '');

// Build the arguments for WP_Query
$args = array(
    'post_type'      => 'wp-manga',
    'posts_per_page' => $posts_per_page, // Will now show 21 manga per page
    'paged'          => $paged,
    'post_status'    => 'publish', // Only get published posts
);

// If there is a search keyword, sanitize and add it to the query
if (!empty($search_keyword)) {
    $search_keyword = sanitize_text_field($search_keyword);
    
    // Add the search parameter
    $args['s'] = $search_keyword;
    
    // Add filter to search only in titles
    add_filter('posts_search', function($search, $query) use ($search_keyword, $wpdb) {
        if (!empty($query->query_vars['s'])) {
            $search_term = $wpdb->esc_like($search_keyword);
            return $wpdb->prepare(
                " AND {$wpdb->posts}.post_title LIKE %s ",
                '%' . $search_term . '%'
            );
        }
        return $search;
    }, 10, 2);
    
    // Add custom ordering for relevance
    add_filter('posts_orderby', function($orderby, $query) use ($search_keyword, $wpdb) {
        if (!empty($query->query_vars['s'])) {
            $search_term = $wpdb->esc_like($search_keyword);
            return $wpdb->prepare("
                CASE 
                    WHEN {$wpdb->posts}.post_title LIKE %s THEN 1
                    WHEN {$wpdb->posts}.post_title LIKE %s THEN 2
                    WHEN {$wpdb->posts}.post_title LIKE %s THEN 3
                    ELSE 4
                END ASC, 
                {$wpdb->posts}.post_date DESC
            ", 
            $search_term,                    // Exact match
            $search_term . '%',              // Starts with
            '%' . $search_term . '%'         // Contains
            );
        }
        return $orderby;
    }, 10, 2);
}

// Add meta query to filter adult content if needed
if (!$show_adult) {
    $args['meta_query'] = array(
        array(
            'key'     => 'manga_adult_content',
            'value'   => 'yes',
            'compare' => 'NOT LIKE',
        )
    );
}

// Filter by manga type if any types are selected
if (!empty($_GET['type'])) {
    $selected_types = array_map('sanitize_text_field', (array) $_GET['type']);
    $args['tax_query'][] = array(
        'taxonomy' => 'wp-manga-type',
        'field'    => 'slug',
        'terms'    => $selected_types,
        'operator' => 'IN', // Can be 'IN', 'NOT IN', 'AND'
    );
}

// Filter by genres if any genres are selected
if (!empty($_GET['genre'])) {
    $selected_genres = array_map('intval', (array) $_GET['genre']);
    if (isset($_GET['genre_mode']) && $_GET['genre_mode'] === 'and') {
        // If "Must have all the selected genres" is checked
        $args['tax_query'][] = array(
            'taxonomy' => 'wp-manga-genre',
            'field'    => 'term_id',
            'terms'    => $selected_genres,
            'operator' => 'AND', // Require all selected genres
        );
    } else {
        // Default behavior is to match any of the selected genres
        $args['tax_query'][] = array(
            'taxonomy' => 'wp-manga-genre',
            'field'    => 'term_id',
            'terms'    => $selected_genres,
            'operator' => 'IN', // Match any selected genre
        );
    }
}

// Filter by release year if any years are selected
if (!empty($_GET['release'])) {
    $selected_years = array_map('sanitize_text_field', (array) $_GET['release']);
    $args['tax_query'][] = array(
        'taxonomy' => 'wp-manga-release',
        'field'    => 'slug',
        'terms'    => $selected_years,
        'operator' => 'IN',
    );
}


// Filter by status if any statuses are selected
if (!empty($_GET['status'])) {
    $selected_statuses = array_map('sanitize_text_field', (array) $_GET['status']);
    $args['meta_query'][] = array(
        'key'     => '_wp_manga_status',
        'value'   => $selected_statuses,
        'compare' => 'IN', // Match any selected status
    );
}

// Fetch the sort option from the query parameters
$sort_option = isset($_GET['sort']) ? sanitize_text_field($_GET['sort']) : 'recently_added'; // Default to 'recently_added'

// Modify the query based on the selected sorting option
switch ($sort_option) {
    case 'title_az':
        $args['orderby'] = 'title';
        $args['order'] = 'ASC';
        break;

    case 'recently_added':
        // Remove the meta key sorting
        unset($args['meta_key']);
        
        // Add custom ordering using manga chapters table
        add_filter('posts_join', function($join, $query) {
            global $wpdb;
            if (!empty($query->query_vars['post_type']) && $query->query_vars['post_type'] === 'wp-manga') {
                $join .= " LEFT JOIN {$wpdb->prefix}manga_chapters ON ({$wpdb->posts}.ID = {$wpdb->prefix}manga_chapters.post_id)";
            }
            return $join;
        }, 10, 2);

        add_filter('posts_orderby', function($orderby, $query) {
            global $wpdb;
            if (!empty($query->query_vars['post_type']) && $query->query_vars['post_type'] === 'wp-manga') {
                return "MAX({$wpdb->prefix}manga_chapters.date) DESC";
            }
            return $orderby;
        }, 10, 2);

        add_filter('posts_groupby', function($groupby, $query) {
            global $wpdb;
            if (!empty($query->query_vars['post_type']) && $query->query_vars['post_type'] === 'wp-manga') {
                return "{$wpdb->posts}.ID";
            }
            return $groupby;
        }, 10, 2);
        break;

    case 'most_viewed':
        $args['meta_key'] = '_wp_manga_views';
        $args['orderby'] = 'meta_value_num';
        $args['order'] = 'DESC';
        break;

    // Add other sorting options if needed
}

// Now, fetch posts with the updated sorting options
$manga_posts = new WP_Query($args);
remove_all_filters('posts_search');
remove_all_filters('posts_orderby');

// Get total number of results
$total_results = $manga_posts->found_posts;

// After the WP_Query, remove the filters we added
if ($sort_option === 'recently_added') {
    remove_all_filters('posts_join');
    remove_all_filters('posts_orderby');
    remove_all_filters('posts_groupby');
}

get_header();
?>

<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

<main class="">
    <div class="container">
        <section class="mt-5">
            <div class="head">
                <h2>
                    <?php
                    // Get the current sort option
                    $current_sort = isset($_GET['sort']) ? sanitize_text_field($_GET['sort']) : 'recently_added';
                    
                    // Check if there's a search query
                    $search_query = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
                    
                    if (!empty($search_query)) {
                        printf('Résultats pour : %s', esc_html($search_query));
                    } else {
                        switch ($current_sort) {
                            case 'title_az':
                                echo 'A à Z';
                                break;
                            case 'most_viewed':
                                echo 'Populaire';
                                break;
                            case 'recently_added':
                            default:
                                echo 'Récent';
                                break;
                        }
                    }
                    ?>
                </h2>
            </div>
            
            <form id="filters" action="<?php echo esc_url(home_url('/')); ?>" role="search" method="get">
                <input type="hidden" name="post_type" value="wp-manga" />
                <div>
                    <div class="search">
                        <input type="text" 
                               class="form-control" 
                               placeholder="Rechercher..." 
                               name="s" 
                               value="<?php echo isset($_GET['s']) ? esc_attr($_GET['s']) : ''; ?>"
                               style="font-size: 16px"
                               autocomplete="off"
                               onkeydown="if(event.key === 'Enter') { 
                                   event.preventDefault(); 
                                   document.getElementById('filters').submit();
                               }" />
                    </div>
                    <div>
                        <div class="dropdown">
                            <button type="button" data-toggle="dropdown">Type</button>
                            <ul class="dropdown-menu noclose c1">
                                <?php
                                $terms = get_terms(array(
                                    'taxonomy' => 'wp-manga-type',
                                    'hide_empty' => false,
                                ));

                                if (!empty($terms) && !is_wp_error($terms)) :
                                    foreach ($terms as $term) :
                                        $checked = (isset($_GET['type']) && in_array($term->slug, (array)$_GET['type'])) ? 'checked' : '';
                                ?>
                                        <li>
                                            <input type="checkbox" id="type-<?php echo esc_attr($term->slug); ?>" name="type[]" value="<?php echo esc_attr($term->slug); ?>" <?php echo $checked; ?> />
                                            <label for="type-<?php echo esc_attr($term->slug); ?>"><?php echo esc_html($term->name); ?></label>
                                        </li>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <div class="dropdown">
                            <button type="button" data-toggle="dropdown"><span class="value" data-placeholder="Genres" data-label-placement="true">Genres</span></button>
                            <ul class="dropdown-menu noclose c1">
                                <?php
                                $genres = get_terms(array(
                                    'taxonomy' => 'wp-manga-genre',
                                    'hide_empty' => false,
                                ));

                                if (!empty($genres) && !is_wp_error($genres)) :
                                    // Add "Genre Mode" option at the top
                                    ?>
                                    <li class="genre-mode-option">
                                        <input type="checkbox" id="genre-mode" name="genre_mode" value="and" 
                                            <?php echo (isset($_GET['genre_mode']) && $_GET['genre_mode'] === 'and') ? 'checked' : ''; ?> />
                                        <label for="genre-mode">Inclure les genres choisis</label>
                                    </li>
                                    <li class="dropdown-divider"></li>
                                    <?php
                                    foreach ($genres as $genre) :
                                        $checked = (isset($_GET['genre']) && in_array($genre->term_id, (array)$_GET['genre'])) ? 'checked' : '';
                                        ?>
                                        <li>
                                            <input type="checkbox" id="genre-<?php echo esc_attr($genre->term_id); ?>" 
                                                   name="genre[]" 
                                                   value="<?php echo esc_attr($genre->term_id); ?>" 
                                                   <?php echo $checked; ?> />
                                            <label for="genre-<?php echo esc_attr($genre->term_id); ?>">
                                                <?php echo esc_html($genre->name); ?>
                                            </label>
                                        </li>
                                    <?php
                                    endforeach;
                                endif;
                                ?>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <div class="dropdown">
                            <button type="button" data-toggle="dropdown"><span class="value" data-placeholder="Année " data-label-placement="true">Année </span></button>
                            <ul class="dropdown-menu noclose c1">
                                <?php
                                // Get all release years from the taxonomy
                                $years = get_terms(array(
                                    'taxonomy' => 'wp-manga-release',
                                    'hide_empty' => false,
                                    'orderby' => 'name',
                                    'order' => 'DESC', // Most recent years first
                                ));

                                if (!empty($years) && !is_wp_error($years)) :
                                    foreach ($years as $year) :
                                        $checked = (isset($_GET['release']) && in_array($year->slug, (array)$_GET['release'])) ? 'checked' : '';
                                        ?>
                                        <li>
                                            <input type="checkbox" 
                                                   id="release-<?php echo esc_attr($year->slug); ?>" 
                                                   name="release[]" 
                                                   value="<?php echo esc_attr($year->slug); ?>" 
                                                   <?php echo $checked; ?> />
                                            <label for="release-<?php echo esc_attr($year->slug); ?>">
                                                <?php echo esc_html($year->name); ?>
                                            </label>
                                        </li>
                                    <?php
                                    endforeach;
                                endif;
                                ?>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <div class="dropdown">
                            <button type="button" data-toggle="dropdown"><span class="value" data-placeholder="Status" data-label-placement="true">Status</span></button>
                            <ul class="dropdown-menu noclose c1">
                                <?php
                                if (!empty($unique_statuses)) :
                                    foreach ($unique_statuses as $status) :
                                        $label = isset($status_mapping[$status]) ? $status_mapping[$status] : $status;
                                        $checked = (isset($_GET['status']) && in_array($status, (array)$_GET['status'])) ? 'checked' : '';
                                ?>
                                        <li>
                                            <input type="checkbox" id="status-<?php echo esc_attr($status); ?>" name="status[]" value="<?php echo esc_attr($status); ?>" <?php echo $checked; ?> />
                                            <label for="status-<?php echo esc_attr($status); ?>"><?php echo esc_html($label); ?></label>
                                        </li>
                                <?php
                                    endforeach;
                                endif;
                                ?>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <div class="dropdown">
                            <button type="button" data-toggle="dropdown"><span class="value" data-placeholder="Trier par" data-label-placement="true">Trier par</span></button>
                            <ul class="dropdown-menu noclose c1 dropdown-menu-right dropdown-menu-xs-left">
                                <li>
                                    <input type="radio" id="sort-recently_added" name="sort" value="recently_added" <?php echo isset($_GET['sort']) && $_GET['sort'] === 'recently_added' ? 'checked' : ''; ?> />
                                    <label for="sort-recently_added">Récent</label>
                                </li>
                                <li>
                                    <input type="radio" id="sort-title_az" name="sort" value="title_az" <?php echo isset($_GET['sort']) && $_GET['sort'] === 'title_az' ? 'checked' : ''; ?> />
                                    <label for="sort-title_az">A à Z</label>
                                </li>
                                <li>
                                    <input type="radio" id="sort-most_viewed" name="sort" value="most_viewed" <?php echo isset($_GET['sort']) && $_GET['sort'] === 'most_viewed' ? 'checked' : ''; ?> />
                                    <label for="sort-most_viewed">Populaire</label>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fa-solid fa-magnifying-glass"></i>
                            <span>Filtrer</span>
                            <i class="ml-2 bi bi-intersect"></i>
                        </button>
                    </div>
                </div>
            </form>

            <div class="original card-lg">
                <?php
                // Loop through the posts
                if ($manga_posts->have_posts()) :
                    while ($manga_posts->have_posts()) : $manga_posts->the_post();
                    $manga_id = get_the_ID();
                    $post_link = get_permalink();
                    $thumbnail = get_the_post_thumbnail_url($manga_id, 'manga_cover');
                    $chapters = get_latest_chapters($manga_id, 2);
                    ?>
                    <div class="unit item-<?php echo esc_attr($manga_id); ?>">
                        <div class="inner">
                            <a href="<?php echo esc_url($post_link); ?>" class="poster" data-tip="<?php echo esc_attr($manga_id); ?>">
                                <div class="poster-image-wrapper">
                                    <?php
                                    // Get comic type and flag
                                    $types = wp_get_post_terms($manga_id, 'wp-manga-type');
                                    $comic_type = !empty($types) ? $types[0]->name : '';
                                    $flag_path = get_stylesheet_directory_uri() . '/assets/images/flags/';
                                    
                                    // Get flag image path
                                    $type_to_flag = [
                                        'manga'  => 'jp.svg',
                                        'manhwa' => 'kr.svg',
                                        'manhua' => 'cn.svg'
                                    ];
                                    $flag_image = isset($type_to_flag[strtolower($comic_type)]) 
                                        ? $flag_path . $type_to_flag[strtolower($comic_type)]
                                        : '';
                                    
                                    if($flag_image): ?>
                                    <div class="manga-flag width-limit" data-type="<?php echo esc_attr(strtolower($comic_type)); ?>">
                                        <img src="<?php echo esc_url($flag_image); ?>" alt="<?php echo esc_attr($comic_type); ?>" title="<?php echo esc_attr($comic_type); ?>" class="flag-icon">
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!is_wp_error($thumbnail) && !empty($thumbnail)) : ?>
                                        <img src="<?php echo esc_url($thumbnail); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" style="height: 100%; object-fit: cover;" />
                                    <?php endif; ?>
                                </div>
                            </a>
                            <div class="info">
                                <a href="<?php echo esc_url($post_link); ?>"><?php echo esc_html(get_the_title()); ?></a>
                                <ul class="content" data-name="chap">
                                    <?php
                                    if ($chapters) {
                                        foreach ($chapters as $chapter) {
                                            // Get chapter ID to check for premium status
                                            global $wpdb;
                                            $chapter_id = $wpdb->get_var($wpdb->prepare(
                                                "SELECT chapter_id FROM {$wpdb->prefix}manga_chapters WHERE post_id = %d AND chapter_slug = %s",
                                                $manga_id,
                                                $chapter->chapter_slug
                                            ));

                                            $is_premium = false;
                                            if ($chapter_id) {
                                                $is_premium = get_post_meta($chapter_id, '_is_premium_chapter', true);
                                            }

                                            $chapter_link = esc_url(get_permalink($manga_id) . $chapter->chapter_slug);
                                            if ($is_premium === '1') {
                                                if (!is_user_logged_in()) {
                                                    $chapter_link = esc_url(home_url('/connexion'));
                                                } elseif (function_exists('is_user_subscribed') && !is_user_subscribed()) {
                                                    $chapter_link = esc_url(home_url('/subscription/'));
                                                }
                                            }
                                            ?>
                                            <li>
                                                <a href="<?php echo $chapter_link; ?>">
                                                    <span class="ch-num">Ch. <?php echo esc_html($chapter->chapter_name); ?></span>
                                                    <span class="ch-date">
                                                        <?php if ($is_premium === '1'): ?>
                                                            <span class="premium-ch">Premium</span>
                                                        <?php else: ?>
                                                            <?php echo esc_html(time_ago($chapter->date)); ?>
                                                        <?php endif; ?>
                                                    </span>
                                                </a>
                                            </li>
                                        <?php }
                                    } ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <?php endwhile;
                else :
                    echo '<p class="s-p">No results found</p>';
                endif;

                // Reset post data
                wp_reset_postdata();
                ?>
            </div>

            <nav class="navigation">
                <ul class="pagination">
                    <li class="page-item <?php if ($paged == 1) echo 'disabled'; ?>">
                        <a class="page-link" href="<?php echo esc_url(get_pagenum_link(1)); ?>" rel="first">«</a>
                    </li>
                    <li class="page-item <?php if ($paged <= 1) echo 'disabled'; ?>">
                        <a class="page-link" href="<?php echo esc_url(get_pagenum_link($paged - 1)); ?>" rel="prev" aria-label="« Previous">‹</a>
                    </li>

                    <?php
                    // Determine the range of pages to display
                    $range = 2; // 2 pages before and after
                    $show_page_numbers = [];

                    // Set up the start and end numbers for pagination
                    for ($i = max(1, $paged - $range); $i <= min($manga_posts->max_num_pages, $paged + $range); $i++) {
                        $show_page_numbers[] = $i;
                    }

                    // Display the page numbers
                    foreach ($show_page_numbers as $i) :
                        ?>
                        <li class="page-item <?php if ($i == $paged) echo 'active'; ?>">
                            <a class="page-link" href="<?php echo esc_url(get_pagenum_link($i)); ?>"><?php echo esc_html($i); ?></a>
                        </li>
                    <?php endforeach; ?>

                    <li class="page-item <?php if ($paged >= $manga_posts->max_num_pages) echo 'disabled'; ?>">
                        <a class="page-link" href="<?php echo esc_url(get_pagenum_link($paged + 1)); ?>" rel="next" aria-label="Next »">›</a>
                    </li>
                    <li class="page-item <?php if ($paged == $manga_posts->max_num_pages) echo 'disabled'; ?>">
                        <a class="page-link" href="<?php echo esc_url(get_pagenum_link($manga_posts->max_num_pages)); ?>" rel="last">»</a>
                    </li>
                </ul>
            </nav>

        </section>
        <?php
        $sponsored_options = get_option('sponsored_content_settings');
    if (isset($sponsored_options['enable_manga_search']) && $sponsored_options['enable_manga_search']) {
        $link = !empty($sponsored_options['link_manga_search']) ? esc_url($sponsored_options['link_manga_search']) : '#';
        $image = !empty($sponsored_options['image_manga_search']) ? esc_url($sponsored_options['image_manga_search']) : ''; // Default to empty
        if (!empty($image)) { // Only display if image is set
        ?>
        <div class="sponsored-content-search">
            <a href="<?php echo $link; ?>" target="_blank">
                <img src="<?php echo $image; ?>" alt="Sponsored Content" style="max-width: 100% !important; width: 100%; margin-bottom: 20px;" />
            </a>
        </div>
    <?php }
    } ?>
    </div>
</main>

<?php get_footer(); ?>